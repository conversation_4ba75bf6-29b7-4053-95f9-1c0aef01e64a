# RajaPicker Temporary Upload Mode

## Overview

RajaPicker sekarang mendukung temporary upload mode yang meniru cara kerja FilamentPHP FileUpload component. Dalam mode ini, file diupload ke temporary storage (`livewire-tmp`) terlebih dahulu, kemudian dipindah ke permanent storage saat form di-submit.

## Workflow Comparison

### Traditional Mode (Default)
1. File diupload langsung ke permanent storage
2. Thumbnail dan konversi WebP langsung dibuat
3. Media langsung masuk ke database
4. Jika form gagal submit, file tetap tersimpan

### Temporary Mode (New)
1. File diupload ke `storage/app/livewire-tmp/rajapicker/`
2. File disimpan sebagai temporary dengan metadata
3. Preview tersedia dari temporary location
4. Saat form submit, file dipindah ke permanent storage
5. Thumbnail dan konversi WebP dibuat saat commit
6. Temporary files dibersihkan otomatis

## Usage Examples

### Basic Temporary Upload
```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

RajaPicker::make('image')
    ->collection('products')
    ->temporaryMode() // Enable temporary upload mode
    ->convertWebp()
    ->maxFileSize(5)
```

### Multiple Files with Temporary Mode
```php
RajaPicker::make('gallery')
    ->collection('gallery')
    ->multiple()
    ->temporaryMode(true) // Explicitly enable
    ->maxFiles(10)
    ->previewSize(120)
```

### Conditional Temporary Mode
```php
RajaPicker::make('avatar')
    ->collection('avatars')
    ->temporaryMode(config('app.env') === 'production') // Only in production
    ->convertWebp()
```

## API Endpoints

### Upload to Temporary Storage
```
POST /api/media/upload
Content-Type: multipart/form-data

Parameters:
- file: File to upload
- collection: Collection name
- convertWebp: true/false
- temporary: true (for temporary mode)
```

### Commit Temporary Files
```
POST /api/media/commit-temporary
Content-Type: application/json

Body:
{
  "temp_files": [
    {
      "temp_path": "livewire-tmp/rajapicker/rajapicker_abc123.jpg",
      "collection": "products",
      "convert_webp": true,
      "original_name": "product.jpg"
    }
  ]
}
```

### Cleanup Temporary Files
```
POST /api/media/cleanup-temporary
Content-Type: application/json

Body:
{
  "temp_paths": [
    "livewire-tmp/rajapicker/rajapicker_abc123.jpg",
    "livewire-tmp/rajapicker/rajapicker_def456.jpg"
  ]
}
```

## JavaScript Events

### Form Submission Handling
```javascript
// Automatic handling - no manual intervention needed
// RajaPicker automatically commits temporary files before form submission

// Manual commit (if needed)
await this.commitTemporaryFiles();

// Manual cleanup (if needed)
await this.cleanupTemporaryFiles();
```

## Configuration

### Temporary Directory
Default: `storage/app/livewire-tmp/rajapicker/`

### File Naming
Temporary files use pattern: `rajapicker_{random40chars}.{extension}`

### Cleanup
- Automatic cleanup on form submission
- Manual cleanup via API
- Laravel's temporary file cleanup handles orphaned files

## Benefits

1. **Better UX**: Users can preview files before final submission
2. **Rollback Support**: Failed form submissions don't leave orphaned files
3. **Performance**: Thumbnail generation only happens on successful submission
4. **Consistency**: Matches FilamentPHP FileUpload behavior
5. **Storage Efficiency**: No permanent files until form is actually submitted

## Migration from Traditional Mode

Existing code continues to work without changes. To enable temporary mode:

```php
// Before
RajaPicker::make('image')->collection('products')

// After
RajaPicker::make('image')->collection('products')->temporaryMode()
```

## Troubleshooting

### Temporary Files Not Cleaned Up
Check Laravel's temporary file cleanup configuration in `config/livewire.php`:

```php
'temporary_file_upload' => [
    'directory' => 'livewire-tmp',
    // ... other settings
],
```

### Form Submission Issues
Ensure JavaScript is enabled and no errors in browser console. The component automatically handles form submission events.

### Storage Permissions
Ensure `storage/app/livewire-tmp/` directory is writable:

```bash
chmod -R 755 storage/app/livewire-tmp/
```
